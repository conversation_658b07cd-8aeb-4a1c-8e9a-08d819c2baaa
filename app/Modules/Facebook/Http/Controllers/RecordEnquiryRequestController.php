<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use App\Modules\Facebook\Models\LeadStatus;
use App\Modules\Facebook\ValueObjects\LeadData;
use App\Modules\Facebook\Models\RecordEnquiryRequest;
use App\Modules\Facebook\Models\RecordEnquiryRequestStatus;

class RecordEnquiryRequestController
{
    public function index(Request $request): View
    {
        $stats = $this->getStatistics($request);
        return view('facebook::record-enquiry-requests.index', compact('stats'));
    }

    public function getStatistics(Request $request): array
    {
        $vendorId = Auth::user()->getBusinessId();
        $query = RecordEnquiryRequest::query()->where('vendor_id', $vendorId);

        $query->when($request && $request->has('from_date') && $request->has('to_date'), function ($query) use ($request) {
            $fromDate = Carbon::parse($request->input('from_date'))->startOfDay();
            $toDate = Carbon::parse($request->input('to_date'))->endOfDay();
            $query->whereBetween('created_at', [$fromDate, $toDate]);
        }, function ($query) {
            $today = Carbon::now()->startOfDay();
            $query->whereDate('created_at', '>=', $today);
        });

        $totalRecords = $query->count();

        $successCount = (clone $query)->where('status', RecordEnquiryRequestStatus::Success)->count();
        $failedCount = (clone $query)->where('status', RecordEnquiryRequestStatus::Failed)->count();

        $newLeadsCount = (clone $query)->where('lead_status', LeadStatus::New)->count();
        $duplicateLeadsCount = (clone $query)->where('lead_status', LeadStatus::Duplicate)->count();
        $pendingLeadsCount = (clone $query)->where('lead_status', LeadStatus::Pending)->count();

        return [
            'total_records' => $totalRecords,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'new_leads_count' => $newLeadsCount,
            'duplicate_leads_count' => $duplicateLeadsCount,
            'pending_lead_data_count' => $pendingLeadsCount,
        ];
    }

    public function getRecordEnquiryRequests(Request $request): JsonResponse
    {
        $vendorId = Auth::user()->getBusinessId();

        $query = RecordEnquiryRequest::query()
            ->where('vendor_id', $vendorId)
            ->when($request->has('from_date') && $request->has('to_date'), function ($query) use ($request) {
                $fromDate = Carbon::parse($request->input('from_date'))->startOfDay();
                $toDate = Carbon::parse($request->input('to_date'))->endOfDay();
                $query->whereBetween('created_at', [$fromDate, $toDate]);
            }, function ($query) {
                $today = Carbon::now()->startOfDay();
                $query->whereDate('created_at', '>=', $today);
            })
            ->select([
                'id',
                'page_id',
                'form_id',
                'lead_gen_id',
                'vendor_id',
                'status',
                'failure_reason',
                'lead_data',
                'lead_status',
                'created_at',
                'updated_at',
            ])
            ->orderBy('id', 'DESC');

        return DataTables::of($query)
            ->addIndexColumn()
            ->editColumn('created_at', function (RecordEnquiryRequest $request) {
                return $request->created_at->format('Y-m-d H:i:s');
            })
            ->addColumn('name', function (RecordEnquiryRequest $request) {
                if (!$request->lead_data || !$request->lead_data->name) {
                    return 'N/A';
                }
                return htmlspecialchars($request->lead_data->name);
            })
            ->addColumn('phone_number', function (RecordEnquiryRequest $request) {
                if (!$request->lead_data || !$request->lead_data->phoneNumber) {
                    return 'N/A';
                }
                return htmlspecialchars($request->lead_data->phoneNumber);
            })
            ->editColumn('status', function (RecordEnquiryRequest $request) {
                $statusClass = match($request->status) {
                    RecordEnquiryRequestStatus::Pending => 'badge-warning',
                    RecordEnquiryRequestStatus::Success => 'badge-success',
                    RecordEnquiryRequestStatus::Failed => 'badge-danger',
                    default => 'badge-secondary',
                };

                $statusText = match($request->status) {
                    RecordEnquiryRequestStatus::Pending => 'Pending',
                    RecordEnquiryRequestStatus::Success => 'Success',
                    RecordEnquiryRequestStatus::Failed => 'Failed',
                    default => 'Unknown',
                };

                return '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
            })
            ->editColumn('lead_status', function (RecordEnquiryRequest $request) {
                if (!$request->lead_status) {
                    return '<span class="badge badge-secondary">N/A</span>';
                }

                $leadStatusClass = match($request->lead_status) {
                    LeadStatus::Pending => 'badge-warning',
                    LeadStatus::New => 'badge-success',
                    LeadStatus::Duplicate => 'badge-info',
                    default => 'badge-secondary',
                };

                $leadStatusText = match($request->lead_status) {
                    LeadStatus::Pending => 'Pending',
                    LeadStatus::New => 'New',
                    LeadStatus::Duplicate => 'Duplicate',
                    default => 'Unknown',
                };

                return '<span class="badge ' . $leadStatusClass . '">' . $leadStatusText . '</span>';
            })
            ->editColumn('lead_data', function (RecordEnquiryRequest $request) {
                if (!$request->lead_data instanceof LeadData) {
                    return 'No additional data';
                }

                $leadData = $request->lead_data;
                $hasAdditionalData = false;
                $html = '<button type="button" class="btn btn-sm btn-info view-lead-data" data-toggle="tooltip" data-html="true" title="';

                if ($leadData->email) {
                    $html .= 'Email: ' . htmlspecialchars($leadData->email) . '<br>';
                    $hasAdditionalData = true;
                }

                if (!empty($leadData->metadata)) {
                    foreach ($leadData->metadata as $key => $value) {
                        if (is_string($value)) {
                            $html .= htmlspecialchars($key) . ': ' . htmlspecialchars($value) . '<br>';
                            $hasAdditionalData = true;
                        }
                    }
                }

                if (!$hasAdditionalData) {
                    return 'No additional data';
                }

                $html .= '"><i class="fa fa-eye"></i></button>';

                return $html;
            })
            ->rawColumns(['status', 'lead_status', 'lead_data'])
            ->make(true);
    }

    public function getStatisticsData(Request $request): JsonResponse
    {
        $stats = $this->getStatistics($request);
        return response()->json($stats);
    }
}
